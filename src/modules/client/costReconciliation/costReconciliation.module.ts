import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CostReconciliation } from '../../common/entity/costReconciliation.entity';
import { CostReconciliationService } from './costReconciliation.service';
import { CostReconciliationResolver } from './costReconciliation.resolver';
import { CostReconciliationCodeService } from '../../common/module/costReconciliationCode/costReconciliationCode.service';
import { CostReconciliationCodeResolver } from './costReconciliationCode.resolver';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';

@Module({
  imports: [TypeOrmModule.forFeature([CostReconciliation])],
  providers: [
    CostReconciliationService,
    CostReconciliationResolver,
    CostReconciliationCodeService,
    CostReconciliationCodeResolver,
    JwtService,
    SBLogger,
  ],
  exports: [CostReconciliationService, CostReconciliationCodeService],
})
export class CostReconciliationModule {}
