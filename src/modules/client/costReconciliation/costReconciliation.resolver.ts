import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { CostReconciliationService } from './costReconciliation.service';
import {
  GetCostReconciliationInput,
  CostReconciliationListResponse,
  GetReconciliationCodeByMonthInput,
  ReconciliationCodeResponse,
} from './costReconciliation.input';

@Resolver()
export class CostReconciliationResolver {
  constructor(private costReconciliationService: CostReconciliationService) {}

  /**
   * Helper method to extract clientId from authentication context
   */
  private getClientIdFromContext(context: any): string {
    const clientId = context.req.user?.id || context.req.user?.clientId;

    if (!clientId) {
      throw new Error('Client ID not found in authentication context');
    }

    return clientId;
  }

  // Cost Reconciliation Resolvers
  @Query(() => CostReconciliationListResponse)
  async clientCostReconciliations(
    @Args('input') input: GetCostReconciliationInput,
    @Context() context: any,
  ): Promise<CostReconciliationListResponse> {
    // Auto-fill clientId from context for client users
    const clientId = input.clientId || this.getClientIdFromContext(context);
    const inputWithClientId = { ...input, clientId };

    return this.costReconciliationService.getCostReconciliations(
      inputWithClientId,
    );
  }

  // Get reconciliation code by month
  @Query(() => ReconciliationCodeResponse, { nullable: true })
  async getReconciliationCodeByMonth(
    @Args('input') input: GetReconciliationCodeByMonthInput,
    @Context() context: any,
  ): Promise<ReconciliationCodeResponse | null> {
    // Get clientId from context (auto-filled for client users)
    const clientId = input.clientId || this.getClientIdFromContext(context);

    return this.costReconciliationService.getReconciliationCodeByMonth(
      clientId,
      input.month,
    );
  }
}
