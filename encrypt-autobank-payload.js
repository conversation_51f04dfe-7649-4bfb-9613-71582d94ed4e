const { publicEncrypt, constants } = require('crypto');

// ======== HARD-CODED PUBLIC KEY ========
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

// ======== DATA TO ENCRYPT ========
const payload = {
  refCode: 'SPS 6AeiwDZVaRqodF6SfVQcJa upWdSPE',
  amount: 140000,
};

// ======== ENCRYPT ========
try {
  const bufferData = Buffer.from(JSON.stringify(payload), 'utf-8');

  const encrypted = publicEncrypt(
    {
      key: publicKey,
      padding: constants.RSA_PKCS1_OAEP_PADDING,
    },
    bufferData,
  );

  const base64Encoded = encrypted.toString('base64');
  console.log('🔐 Encrypted base64:\n');
  console.log(base64Encoded);
} catch (err) {
  console.error('❌ Encryption failed:', err);
}




curl -X POST \
  http://localhost:3001/autobank/callback \
  -H "Content-Type: application/json" \
  -d '{
    "data": "KvfRjDIuPteXZsfRIsZEcyJWdgbh6qdeiPRQk0fO3dU1TyM+JUUGM/I6iTz/2nl9uosdihlIiW5YGnQMJRAJtrZIZiWsLbiz5CpqjQOTnMzJbc+Ky96iwK1jguOIQrva8S3phwExWtodAEJMjtpB9b/TmrYhvdm29UBIgJL68Ie7WkDYB7Kztg+Sk/zuSTgloE0iLaQTsHaUc1fa7H9SypwueEszKk+umblPMA5CtelCgOu9mXEbGB0uU5Lk6bO0PQmXoFZzIcZwee0uZXP5AzrGqn0rzSHrBPQlV3wTyIEMFre/N/EZTyzqpn8v3CIra+owIbL3PUDIIJhWlbG3rA=="
  }'