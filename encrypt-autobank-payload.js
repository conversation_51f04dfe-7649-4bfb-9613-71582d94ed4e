const { publicEncrypt, constants } = require('crypto');

// ======== HARD-CODED PUBLIC KEY ========
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

// ======== DATA TO ENCRYPT ========
const payload = {
  refCode: 'SPS 6AeiwDZVaRqodF6SfVQcJa ATJ4SPE',
  amount: 140000,
};

// ======== ENCRYPT ========
try {
  const bufferData = Buffer.from(JSON.stringify(payload), 'utf-8');

  const encrypted = publicEncrypt(
    {
      key: publicKey,
      padding: constants.RSA_PKCS1_OAEP_PADDING,
    },
    bufferData,
  );

  const base64Encoded = encrypted.toString('base64');
  console.log('🔐 Encrypted base64:\n');
  console.log(base64Encoded);
} catch (err) {
  console.error('❌ Encryption failed:', err);
}




curl -X POST \
  http://localhost:3001/autobank/callback \
  -H "Content-Type: application/json" \
  -d '{
    "data": "TH8+u8C+8iGbQVDbP8T/Km4cY78ZE/WP6psJwnRjx/BIAMdhMER6dO0A4iJG81pEBH8pjjugumdYpx25YSve01MbtovERlWrAiGg3QdeYW0tWCX++QK9cYPKWK7twOVEzH+bAoyJa3GXPK1UisP0RKKeoSlE4zDD0OofxqpSJ8ddd4ononQhnI8I+cvUr1/acBtipYQAzEFzGaTVz85NsRl/26+GcjLQm+uxYLajB8zvX6Wkoo8zsKELj1EgjQaKwh9TdWuRDa/9yS96DjGg7QM1eLnlZmyWZJS4wKBSZch42fu69ewLVGg6wQ8ek2U10y/EZ7U4rTlMS56bcbksYA=="
  }'