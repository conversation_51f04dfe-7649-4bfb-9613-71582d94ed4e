const { publicEncrypt, constants } = require('crypto');

// ======== HARD-CODED PUBLIC KEY ========
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

// ======== DATA TO ENCRYPT ========
const payload = {
  refCode: 'SPS 6AeiwDZVaRqodF6SfVQcJa hR9LSPE',
  amount: 140000,
};

// ======== ENCRYPT ========
try {
  const bufferData = Buffer.from(JSON.stringify(payload), 'utf-8');

  const encrypted = publicEncrypt(
    {
      key: publicKey,
      padding: constants.RSA_PKCS1_OAEP_PADDING,
    },
    bufferData,
  );

  const base64Encoded = encrypted.toString('base64');
  console.log('🔐 Encrypted base64:\n');
  console.log(base64Encoded);
} catch (err) {
  console.error('❌ Encryption failed:', err);
}




curl -X POST \
  http://localhost:3001/autobank/callback \
  -H "Content-Type: application/json" \
  -d '{
    "data": "NQPKn+oknHgWRZOdj3w7sTeKu9+d9lnli+ZDjnLpAoNmdB13wuB6PRJf7GFDG1fpmyNkB6t1A1k9pcCmkh4aIBLj8aNiyXhAhHPaFJmqwq6gPQrc+tCYSHkFxf+/8DjO3S1ZlA7I2BKAH1YSNY5EU4e3YG4enwMwAsDRJLtrz/J9VikM6Z9BqRn3fbW1L8AKJwOxJ9icyHS3yxL4kD6wc3KUUlwsyP1MX5L63fGzwO3WxxyW7y2QG7XxgbEFQbqHghMtAv95PpkOT9OvPRI7KdnIh0K7lQslgfUfGKL63dABlFfX5RoM8Esvn2dJzOh6EQ1+e0Wg2rTHpjpC/C8TGw=="
  }'